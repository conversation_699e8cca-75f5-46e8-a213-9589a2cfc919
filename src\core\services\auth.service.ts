import { NextResponse } from 'next/server';
import mongoose from 'mongoose';
import { userRepository, categoryRepository } from '@/core/repositories';
import { JWT_SECRET, JWT_EXPIRY, COOKIE_OPTIONS } from '@/config/auth';
import { KeyAuthRequest, RegisterRequest, KeyRegenerationRequest, JwtPayload } from '@/types/auth';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import { generateToken, generateAccessKey, isValidAccessKeyFormat } from '@/utils/auth';
import { successResponse } from '@/utils/response';
import logger from '@/utils/logger';

/**
 * Authentication service
 */
export class AuthService {
  /**
   * Register a new user
   * @param data Registration data (empty object - system generates access key)
   * @returns Success message with access key
   */
  async register(data: RegisterRequest): Promise<{ message: string; accessKey: string; status: number }> {
    try {
      logger.info('Registration attempt - generating new access key');

      // Generate a unique access key
      let accessKey: string;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        accessKey = generateAccessKey();
        attempts++;

        if (attempts > maxAttempts) {
          throw ApiError.internal('Failed to generate unique access key', ErrorCode.INTERNAL_SERVER_ERROR);
        }
      } while (await userRepository.accessKeyExists(accessKey));

      logger.info(`Generated unique access key: ${accessKey}`);

      // Create new user
      logger.info(`Creating new user with access key: ${accessKey}`);
      const newUser = await userRepository.create({
        accessKey: accessKey.toLowerCase(),
      });
      if (!newUser || !('_id' in newUser)) {
        throw ApiError.internal('Failed to create user', ErrorCode.INTERNAL_SERVER_ERROR);
      }

      logger.info(`User with access key ${accessKey} created successfully`);

      // Create default categories for the new user
      try {
        const userId = (newUser._id as mongoose.Types.ObjectId).toString();
        logger.info(`Creating default categories for user ${userId}`);

        // Define default categories
        const defaultCategories = [
          { name: 'Work', color: '#4f46e5' },
          { name: 'Personal', color: '#10b981' },
          { name: 'Study', color: '#f59e0b' },
          { name: 'Health', color: '#ef4444' },
          { name: 'Leisure', color: '#8b5cf6' }
        ];

        // Create each default category
        for (const category of defaultCategories) {
          await categoryRepository.create(userId, category);
        }

        logger.info(`Default categories created for user ${userId}`);
      } catch (categoryError) {
        // Log the error but don't fail registration if category creation fails
        logger.error('Error creating default categories:', categoryError);
      }

      return {
        message: 'User registered successfully. Please save your access key safely!',
        accessKey: accessKey,
        status: 201
      };
    } catch (error) {
      logger.error('Registration error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Register a new user with a specific access key
   * @param data Registration data with access key and optional name
   * @returns Success message with access key
   */
  async registerWithKey(data: { accessKey: string; name?: string }): Promise<{ message: string; accessKey: string; status: number }> {
    try {
      const { accessKey, name } = data;
      logger.info(`Registration attempt with specific access key: ${accessKey}, name: ${name || 'none'}`);

      // Double-check if access key already exists (defensive programming)
      const keyExists = await userRepository.accessKeyExists(accessKey);
      logger.info(`Double-check - access key exists: ${keyExists}`);

      if (keyExists) {
        logger.warn(`Access key already exists during registerWithKey: ${accessKey}`);
        throw ApiError.conflict('Access key already exists', ErrorCode.DUPLICATE_ACCESS_KEY);
      }

      // Create new user with the provided access key
      logger.info(`Creating new user with access key: ${accessKey}`);
      const newUser = await userRepository.create({
        accessKey: accessKey.toLowerCase(),
        name: name || '',
      });

      if (!newUser || !('_id' in newUser)) {
        logger.error('Failed to create user - no user returned or missing _id');
        throw ApiError.internal('Failed to create user', ErrorCode.INTERNAL_SERVER_ERROR);
      }

      logger.info(`User with access key ${accessKey} created successfully with ID: ${newUser._id}`);

      // Create default categories for the new user
      const userId = newUser._id instanceof mongoose.Types.ObjectId
        ? newUser._id.toString()
        : typeof newUser._id === 'string'
          ? newUser._id
          : String(newUser._id);

      try {
        await categoryRepository.createDefaultCategories(userId);
        logger.info(`Default categories created for user: ${userId}`);
      } catch (categoryError) {
        logger.error('Failed to create default categories:', categoryError);
        // Don't fail registration if category creation fails
      }

      return {
        message: 'Account created successfully! You can now log in with your access key.',
        accessKey: accessKey,
        status: 201
      };
    } catch (error) {
      logger.error('Registration with key error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Authenticate user with access key
   * @param data Authentication data
   * @returns Response with token and user data or error
   */
  async login(data: KeyAuthRequest): Promise<NextResponse> {
    try {
      const { accessKey } = data;

      // Validate access key format
      if (!isValidAccessKeyFormat(accessKey)) {
        throw ApiError.unauthorized('Invalid access key format', ErrorCode.INVALID_CREDENTIALS);
      }

      // Find user by access key
      let user;
      try {
        user = await userRepository.findByAccessKey(accessKey);
      } catch (dbError) {
        logger.error('Database error when finding user');
        throw ApiError.internal(
          'Database error when finding user',
          ErrorCode.DATABASE_ERROR
        );
      }

      if (!user) {
        throw ApiError.unauthorized('Invalid access key', ErrorCode.INVALID_CREDENTIALS);
      }

      // Generate JWT token
      const userId = user._id instanceof mongoose.Types.ObjectId
        ? user._id.toString()
        : typeof user._id === 'string'
          ? user._id
          : String(user._id);

      let token;
      try {
        token = generateToken(
          { id: userId, accessKey: user.accessKey },
          JWT_EXPIRY
        );
      } catch (tokenError) {
        throw ApiError.internal(
          'Error generating authentication token',
          ErrorCode.INTERNAL_SERVER_ERROR
        );
      }

      // Create response with cookie
      const responseData = {
        user: {
          id: userId,
          accessKey: user.accessKey,
          name: user.name || '',
          preferences: user.preferences,
        },
        token,
      };
      const response = successResponse(responseData);

      // Set cookie
      try {
        response.cookies.set('token', token, COOKIE_OPTIONS);
      } catch (cookieError) {
        // Continue anyway since localStorage is a fallback
        logger.error('Error setting cookie');
      }

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal(
        error instanceof Error ? error.message : 'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Logout a user
   * @returns Response with success message
   */
  logout(): NextResponse {
    try {
      // Create response
      const response = successResponse({ message: 'Logged out successfully' });

      // Clear cookie
      response.cookies.set('token', '', {
        ...COOKIE_OPTIONS,
        maxAge: 0,
      });

      return response;
    } catch (error) {
      logger.error('Logout error:', error);
      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get user profile
   * @param userId User ID
   * @returns User profile or error
   */
  async getProfile(userId: string) {
    try {
      const profile = await userRepository.getProfile(userId);

      if (!profile) {
        throw ApiError.notFound('User not found', ErrorCode.USER_NOT_FOUND);
      }

      return profile;
    } catch (error) {
      logger.error('Get profile error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Update user profile
   * @param userId User ID
   * @param data Profile data
   * @returns Updated profile or error
   */
  async updateProfile(userId: string, data: any) {
    try {
      console.log('authService.updateProfile: Received data:', data);
      const { preferences, name } = data;

      // Find user
      const user = await userRepository.findById(userId);
      if (!user) {
        throw ApiError.notFound('User not found', ErrorCode.USER_NOT_FOUND);
      }

      console.log('authService.updateProfile: Current user preferences:', user.preferences);
      console.log('authService.updateProfile: New preferences to merge:', preferences);

      // Update name if provided
      if (name !== undefined) {
        user.name = name.trim();
        console.log('authService.updateProfile: Updated name:', user.name);
      }

      // Update preferences
      if (preferences) {
        user.preferences = { ...user.preferences, ...preferences };
        console.log('authService.updateProfile: Merged preferences:', user.preferences);
      }

      await user.save();
      console.log('authService.updateProfile: User saved successfully');

      const userIdStr = user._id instanceof mongoose.Types.ObjectId
        ? user._id.toString()
        : typeof user._id === 'string'
          ? user._id
          : String(user._id);

      const result = {
        id: userIdStr,
        accessKey: user.accessKey,
        name: user.name || '',
        preferences: user.preferences,
      };

      console.log('authService.updateProfile: Returning result:', result);
      return result;
    } catch (error) {
      logger.error('Update profile error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Regenerate user access key
   * @param userId User ID
   * @param data Key regeneration data
   * @returns New access key or error
   */
  async regenerateAccessKey(userId: string, data: KeyRegenerationRequest) {
    try {
      const { currentAccessKey, newAccessKey: providedNewKey } = data;

      // Find user
      const user = await userRepository.findById(userId);
      if (!user) {
        throw ApiError.notFound('User not found', ErrorCode.USER_NOT_FOUND);
      }

      // Verify current access key
      if (user.accessKey !== currentAccessKey.toLowerCase()) {
        throw ApiError.badRequest('Current access key is incorrect', ErrorCode.INVALID_CREDENTIALS);
      }

      let newAccessKey: string;

      if (providedNewKey) {
        // Use the provided key (from preview)
        logger.info(`Using provided new access key: ${providedNewKey}`);

        // Check if the provided key already exists
        if (await userRepository.accessKeyExists(providedNewKey)) {
          throw ApiError.conflict('Provided access key already exists', ErrorCode.DUPLICATE_ACCESS_KEY);
        }

        newAccessKey = providedNewKey;
      } else {
        // Generate a new unique access key
        logger.info('Generating new random access key');
        let attempts = 0;
        const maxAttempts = 10;

        do {
          newAccessKey = generateAccessKey();
          attempts++;

          if (attempts > maxAttempts) {
            throw ApiError.internal('Failed to generate unique access key', ErrorCode.INTERNAL_SERVER_ERROR);
          }
        } while (await userRepository.accessKeyExists(newAccessKey));
      }

      // Update user's access key
      const updatedUser = await userRepository.updateAccessKey(userId, newAccessKey);
      if (!updatedUser) {
        throw ApiError.internal('Failed to update access key', ErrorCode.INTERNAL_SERVER_ERROR);
      }

      const message = providedNewKey
        ? 'Access key saved successfully!'
        : 'Access key regenerated successfully. Please save your new key safely!';

      return {
        success: true,
        newAccessKey: newAccessKey,
        message
      };
    } catch (error) {
      logger.error('Regenerate access key error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}
